const fs = require('fs');
const path = require('path');

// 读取文件
const filePath = path.join(__dirname, 'src/views/modules/app/rundata.vue');
let content = fs.readFileSync(filePath, 'utf8');

// 查找并替换 registrationSuccessImage 行
const oldPattern = /registrationSuccessImage: 'data:image\/jpeg;base64,[^']+'/;
const newValue = "registrationSuccessImage: require('@/assets/images/registration-success.svg')";

content = content.replace(oldPattern, newValue);

// 写回文件
fs.writeFileSync(filePath, content, 'utf8');
console.log('已成功更新图片引用为 SVG 格式');
