<template>
  <div>
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="search()">
      <el-form-item label="">
        <el-input v-model.trim="dataForm.key" placeholder="手机号/iccid" clearable></el-input>
      </el-form-item>
      <el-form-item label="通道状态">
        <el-select v-model.trim="dataForm.portStatus" clearable placeholder="通道状态">
          <el-option
            v-for="item in portStatusList"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备">
        <el-select v-model.trim="dataForm.equipmentId" clearable placeholder="设备">
          <el-option
            v-for="item in equipmentList"
            :key="item.equipmentId"
            :label="item.description"
            :value="item.equipmentId">
          </el-option>

        </el-select>
      </el-form-item>
      <el-form-item label="流量">
        <el-input-number v-model="dataForm.dateSize" :step="10" step-strictly></el-input-number>
        M
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search()">查询</el-button>
      </el-form-item>

      <el-form-item>
        <el-button type="warning" @click="startRunDataHandle()">启动</el-button>
      </el-form-item>
    </el-form>


    <el-table
      :data="dataList"
      border
      size="mini"
      v-loading="dataListLoading"
      @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50">
      </el-table-column>


      <el-table-column
        prop="activateCardVo.phoneNum"
        header-align="center"
        align="center"
        label="手机号"
        width="100"
      >
      </el-table-column>
      <el-table-column
        prop="activateCardVo.iccid"
        header-align="center"
        align="center"
        label="卡识别码(ICCID)"
        :show-overflow-tooltip="true"
        width="200"
      >
      </el-table-column>

      <el-table-column
        prop="activateCardVo.channel"
        header-align="center"
        align="center"
        label="通道"
        width="50">
      </el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        label="通道状态"
        width="100">
        <template slot-scope="scope">
          <div v-if="scope.row.activateCardVo.portStatus===0">
<!--            <el-tag-->
<!--              type="warning"-->
<!--              disable-transitions>未插卡-->
<!--            </el-tag>-->
          </div>
          <div v-if="scope.row.activateCardVo.portStatus===1">
            <img :src="require('@/assets/img/signal-status/Card_Detected.svg')" alt="检查到卡" style="width: 20px; height: 20px; margin-right: 5px;" />
<!--            <el-tag-->
<!--              type="info"-->
<!--              disable-transitions>检查到卡-->
<!--            </el-tag>-->
          </div>
          <div v-if="scope.row.activateCardVo.portStatus===5">
            <img :src="require('@/assets/img/signal-status/Card_Detected.svg')" alt="已插卡" style="width: 20px; height: 20px; margin-right: 5px;" />
            <el-tag
              type="info"
              disable-transitions>已插卡
            </el-tag>
          </div>
          <div v-if="scope.row.activateCardVo.portStatus===2">
            <el-tag
              type="warning"
              disable-transitions>注册中
            </el-tag>
          </div>
          <div v-if="scope.row.activateCardVo.portStatus===3" style="display: flex; align-items: center; justify-content: center;">
            <el-tag
              type="success"
              disable-transitions>注册成功
            </el-tag>
          </div>
          <div v-if="scope.row.activateCardVo.portStatus===4">
            <img :src="require('@/assets/img/signal-status/Dial_OK.svg')" alt="拨号成功" style="width: 20px; height: 20px; margin-right: 5px;" />
<!--            <el-tag-->
<!--              type="success"-->
<!--              disable-transitions>拨号成功-->
<!--            </el-tag>-->
          </div>
          <div v-if="scope.row.activateCardVo.portStatus===6">
            <el-tag
              type="error"
              disable-transitions>故障
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        label="通道状态"
        width="100">
        <template slot-scope="scope">
          <div v-if="scope.row.cardInfoEntity.pinStatus===0">
            <el-tag
              type="success"
              disable-transitions>解锁成功
            </el-tag>
          </div>
          <div v-if="scope.row.cardInfoEntity.pinStatus===1">
            <el-tag
              type="danger"
              disable-transitions>解锁失败
            </el-tag>
          </div>

        </template>
      </el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        label="进度">
        <template slot-scope="scope">
          <div v-if="scope.row.percentage===null">未启动</div>
          <div v-if="scope.row.percentage!==null">
            <el-progress :percentage=scope.row.percentage :status=scope.row.status></el-progress>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="data"
        header-align="center"
        align="center"
        label="消耗流量"
        width="100">
        <template slot-scope="scope">
          {{ scope.row.data }}MB
        </template>
      </el-table-column>
      <el-table-column
        header-align="center"
        align="center"
        prop="vodafoneAuEntity.useData"
        label="累计流量"
        width="100">
        <template slot-scope="scope">
          {{ scope.row.cardInfoEntity.useData }}MB
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        header-align="center"
        label="操作"
        width="100">
        <template slot-scope="scope">
          <el-button type="warning" size="mini" round
                     @click="startRunDataHandle(scope.row.activateCardVo.channel)">启动
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination background
                   @size-change="sizeChangeHandle"
                   @current-change="currentChangeHandle"
                   :current-page="pageIndex"
                   :page-sizes="[32, 50, 100,200,500]"
                   :page-size="pageSize"
                   :total="totalPage"
                   v-if="pageshow"
                   layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <activate-card-error v-if="activateCardErrorVisible" ref="activateCardError"
                         @refreshDataList="getDataList"></activate-card-error>
    <msg-record v-if="msgRecordVisible" ref="msgRecordRef"
                @refreshDataList="getDataList"></msg-record>
    <shopee-log v-if="shopeeLogVisible" ref="shopeeLog" @refreshDataList="getDataList"></shopee-log>
    <register-controller v-if="registerControllerVisible" ref="registerController"
                         @refreshDataList="getDataList"></register-controller>
    <equipment-controller v-if="equipmentControllerVisible" ref="equipmentController"
                          @refreshDataList="condition"></equipment-controller>

  </div>

</template>

<script>
import ActivateCardError from './activatecard-error'
import MsgRecord from './msg-record'
import ShopeeLog from './shopee-log'
import RegisterController from './globe-register-controller.vue'
import EquipmentController from './equipment-controller'

export default {
  data() {
    return {
      dataForm: {
        key: '',
        equipmentId: 19,
        equipmentIds: [],
        appId: '',
        radio: '0',
        portStatus: '',
        registerStatus: '',
        emailVerifyStatues: '',
        emailStatus: '',
        dateSize: '100'
      },
      dataList: [],
      notKnowCards: [],
      pageIndex: 1,
      pageSize: 32,
      totalPage: 0,
      dataListLoading: false,
      pageshow: true,
      dataListSelections: [],
      addOrUpdateVisible: false,
      dataInfoVisible: true,
      activateCardErrorVisible: false,
      shopeeLogVisible: false,
      msgRecordVisible: false,
      displayVisible: false,
      registerControllerVisible: false,
      equipmentControllerVisible: false,
      appEntities: [],
      equipmentList: [],
      user: JSON.parse(sessionStorage.getItem('user') || '{}'),
      cardTypeVos: [],
      countryEntities: [],
      cardTypeVosFilterByCountryId: [],
      errors: [],
      isRecycle: [
        {
          value: '0',
          label: '否'
        },
        {
          value: '1',
          label: '是'
        }],
      emailVerifyStatues: [
        {
          value: '0',
          label: '否'
        },
        {
          value: '1',
          label: '是'
        }],
      options: [{
        value: '0',
        label: '包含',
        children: []
      }, {
        value: '1',
        label: '不包含',
        children: []
      }],
      portStatusList: [
        {
          value: '0',
          label: '未插卡'
        },
        {
          value: '1',
          label: '检查到卡'
        },
        {
          value: '5',
          label: '已插卡'
        },
        {
          value: '2',
          label: '注册中'
        },
        {
          value: '3',
          label: '注册成功'
        },
        {
          value: '4',
          label: '拨号成功'
        },
        {
          value: '6',
          label: '故障'
        }
      ],
      smsStatus: [
        {
          value: '0',
          label: '未发送'
        },
        {
          value: '1',
          label: '已发送'
        }
      ],
      emailStatus: [
        {
          value: '0',
          label: '未添加'
        },
        {
          value: '1',
          label: '已添加'
        }
      ],
      initNum: 0,
      runNum: 0,
      finishNum: 0,
      noSms: 0,
      addEmailFail: 0,
      registerFail: 0,
      errorNum: 0,
      forbidNum: 0,
      voice: 0,
      emailNum: 0,
      registrationSuccessImage: require('@/assets/img/signal-status/Dial_OK.svg')
    }
  },
  components: {ActivateCardError, MsgRecord, ShopeeLog, RegisterController, EquipmentController},
  activated() {
    this.dataInfoVisible = true
    // eslint-disable-next-line no-unused-expressions
    this.getDataList()
    this.condition()
    this.dataInfo()
  },
  methods: {
    selectCardTypeVosByCountryId(prov) {
      this.cardTypeVosFilterByCountryId = []
      this.cardTypeVos.forEach((item) => {
        if (item.countryId === prov) {
          this.cardTypeVosFilterByCountryId.push(item)
        }
      })
    },
    // 获取数据列表
    getDataList() {
      this.displayVisible = true
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/generator/runData/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'key': this.dataForm.key,
          'equipmentId': this.dataForm.equipmentId,
          'countryId': this.dataForm.countryId,
          'portStatus': this.dataForm.portStatus,
          'typeId': this.dataForm.typeId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    notKnowCard() {
      this.notKnowCards = []
      this.$http({
        url: this.$http.adornUrl('/generator/activateCard/notKnowCard'),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.notKnowCards = data.notKnowCards
        } else {
          this.notKnowCards = []
        }
        if (this.notKnowCards.length > 0) {
          // this.$message({
          //   message: '有未识别号码请点击查看！',
          //   type: 'warning',
          //   duration: 1500
          // })
        }
      })
    },
    search() {
      this.pageIndex = 1
      this.getDataList()
      this.pageshow = false
      this.pageshow = true
    },
    getEquipment() {
      this.$http({
        url: this.$http.adornUrl('/generator/equipment/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': 1,
          'limit': 1000
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.equipmentList = data.page.list
        } else {
          this.equipmentList = []
        }
        this.dataListLoading = false
      })
    },

    flash() {
      this.dataListLoading = true
      this.errors = []
      this.$http({
        url: this.$http.adornUrl('/generator/activateCard/flash'),
        method: 'post',
        data: this.$http.adornData({
          'equipmentIds': this.dataForm.equipmentIds
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message({
            message: data.msg,
            type: 'success',
            duration: 1500
          })
        }
        this.dataListLoading = false
      })
    },
    markLabel() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/generator/shopee/markLabel'),
        method: 'get',
        params: this.$http.adornParams({
          'equipmentId': this.dataForm.equipmentId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message({
            message: data.msg,
            type: 'success',
            duration: 1500
          })
        } else {
          this.$message.error(data.msg)
        }
        this.dataListLoading = false
      })
    },
    addToList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/generator/shopeetemp/addToList'),
        method: 'get',
        params: this.$http.adornParams({
          'equipmentId': this.dataForm.equipmentId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message({
            message: data.msg,
            type: 'success',
            duration: 1500
          })
        } else {
          this.$message.error(data.msg)
        }
        this.dataListLoading = false
      })
    },
    // 弹窗关闭时
    errorHandle() {
      this.activateCardErrorVisible = true
      this.$refs.activateCardError.init(this.notKnowCards)
    },

    condition() {
      this.$http({
        url: this.$http.adornUrl(`/generator/url/condition`),
        method: 'get'
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.appEntities = data.appEntities
          this.countryEntities = data.countryEntities
          this.equipmentList = data.equipmentEntities
          this.cardTypeVos = data.cardTypeVos
          let children = []

          this.appEntities.forEach(app => {
            let item = {
              value: '',
              label: ''
            }
            item.value = app.appId
            item.label = app.description
            children.push(item)
          })
          this.options[0].children = children
          this.options[1].children = children
        }
      })
    },
    handleChange(value) {
      console.log(value)
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val

      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
      console.log(this.dataListSelections)
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    deleteHandle(id, cardUseRecordId) {
      this.$confirm(`确定进行号码释放操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/generator/url/delete'),
          method: 'post',
          data: this.$http.adornData({
            'id': id,
            'cardUseRecordId': cardUseRecordId
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    sendHandle(phoneNum) {
      this.$http({
        url: this.$http.adornUrl('/generator/globeregistration/sentSms'),
        method: 'post',
        data: this.$http.adornData({
          'phoneNum': phoneNum
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.getDataList()
            }
          })
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    // 删除
    sale(phoneNum) {
      this.$confirm(`确定标记售出？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/smsinfo/sale'),
          method: 'post',
          data: this.$http.adornData({
            'phoneNum': phoneNum,
            'isSale': 1
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    notSale(phoneNum) {
      this.$confirm(`确定标记未售出？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/smsinfo/sale'),
          method: 'post',
          data: this.$http.adornData({
            'phoneNum': phoneNum,
            'isSale': 0
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    problem(phoneNum) {
      this.$http({
        url: this.$http.adornUrl('/smsinfo/problem'),
        method: 'post',
        data: this.$http.adornData({
          'phoneNum': phoneNum,
          'isProblem': 1
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.getDataList()
            }
          })
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    noProblem(phoneNum) {
      this.$http({
        url: this.$http.adornUrl('/smsinfo/problem'),
        method: 'post',
        data: this.$http.adornData({
          'phoneNum': phoneNum,
          'isProblem': 0
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
            duration: 1500,
            onClose: () => {
              this.getDataList()
            }
          })
        } else {
          this.$message.error(data.msg)
        }
      })
    },

    msgRecordHandle(phoneNum) {
      this.msgRecordVisible = true
      this.$nextTick(() => {
        this.$refs.msgRecordRef.init(phoneNum)
      })
    },
    shopeeLogHandle(id) {
      this.shopeeLogVisible = true
      this.$nextTick(() => {
        this.$refs.shopeeLog.init(id)
      })
    },
    registerControllerHandle(id) {
      this.registerControllerVisible = true
      this.$nextTick(() => {
        this.$refs.registerController.init(id)
      })
    },
    equipmentControllerHandle(id) {
      this.equipmentControllerVisible = true
      this.$nextTick(() => {
        this.$refs.equipmentController.init(id)
      })
    },
    dataInfo() {
      this.$http({
        url: this.$http.adornUrl('/generator/runData/list'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'key': this.dataForm.key,
          'equipmentId': this.dataForm.equipmentId,
          'countryId': this.dataForm.countryId,
          'portStatus': this.dataForm.portStatus,
          'typeId': this.dataForm.typeId
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
          this.totalPage = data.page.total
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        if (this.dataInfoVisible) {
          setTimeout(() => {
            this.dataInfo()
          }, 10000)
        }
      })
    },
    chargeHandle(phoneNum) {
      var list = phoneNum ? [phoneNum] : this.dataListSelections.map(item => {
        return item.activateCardVo.phoneNum
      })
      this.$confirm(`确定提交充值？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/generator/globeregistration/recharge'),
          method: 'post',
          data: this.$http.adornData({
            'list': list
          })
        }).then(({data}) => {
          if (data && data.code === 0) {
            this.$message({
              message: data.msg,
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    startRunDataHandle(id) {
      let ids = []
      ids = id ? [id] : this.dataListSelections.map(item => {
        return item.activateCardVo.channel
      })


      this.dataList.forEach(item => {
        if (item.activateCardVo.portStatus !== null && item.activateCardVo.portStatus === 3) {
          ids.push(item.activateCardVo.channel)
        }
      })


      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl(`/generator/runData/start`),
        method: 'post',
        data: this.$http.adornData({
          'ids': ids,
          'equipmentId': this.dataForm.equipmentId,
          'dataSize': this.dataForm.dateSize
        })
      }).then(({data}) => {
        if (data && data.code === 0) {
          this.$message.success('启动成功')
          this.getDataList()
        } else {
          this.$message.error(data.msg)
        }
        this.dataListLoading = false
      })
    }
  },
  beforeDestroy() {
    this.dataInfoVisible = false
    console.log(this.dataInfoVisible)
  }
}
</script>

<style>


</style>
